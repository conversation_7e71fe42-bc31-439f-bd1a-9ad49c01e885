const { findProductsByNamesV4, formatProductForAI } = require('./productCompare');

/**
 * 产品基础对比服务 - 非AI版本
 * 功能：
 * 1. 复用现有的产品查询和格式化逻辑
 * 2. 直接返回产品的最原始参数数据，不进行AI分析
 * 3. 不包含缓存功能
 * 4. 保持数据的原始性，不添加额外处理
 */

/**
 * 全面收集所有产品的所有参数信息
 * @param {Array} formattedProducts 格式化后的产品数据
 * @returns {Object} 完整参数信息
 */
function organizeProductParameters(formattedProducts) {
  console.log('📊 全面收集所有产品参数信息...');

  const allCategories = new Set();
  const parametersByCategory = {};
  const commonSpecsParameterOrder = {}; // 记录commonSpecs中参数的原始顺序

  // 第一步：收集commonSpecs中的参数顺序作为基准
  formattedProducts.forEach((product, productIndex) => {
    console.log(`  📋 分析产品 ${productIndex + 1}: ${product.name}`);

    // 优先收集 commonSpecs 中的所有参数，保持原始顺序
    if (product.commonSpecs && typeof product.commonSpecs === 'object') {
      Object.keys(product.commonSpecs).forEach(category => {
        allCategories.add(category);

        // 如果这个分类还没有记录过参数顺序，则记录
        if (!commonSpecsParameterOrder[category]) {
          commonSpecsParameterOrder[category] = [];
        }

        if (!parametersByCategory[category]) {
          parametersByCategory[category] = new Map(); // 使用Map保持插入顺序
        }

        const categorySpecs = product.commonSpecs[category];
        if (typeof categorySpecs === 'object' && categorySpecs !== null) {
          Object.keys(categorySpecs).forEach(param => {
            // 如果参数还没有在commonSpecs顺序中记录，则添加
            if (!commonSpecsParameterOrder[category].includes(param)) {
              commonSpecsParameterOrder[category].push(param);
            }
            parametersByCategory[category].set(param, true);
          });
        }
      });
    }
  });

  // 第二步：收集所有 configurations 中的额外参数
  formattedProducts.forEach((product) => {
    if (product.configurations && Array.isArray(product.configurations)) {
      product.configurations.forEach((config) => {
        if (config.specs && typeof config.specs === 'object') {
          Object.keys(config.specs).forEach(category => {
            allCategories.add(category);
            if (!parametersByCategory[category]) {
              parametersByCategory[category] = new Map();
            }
            if (!commonSpecsParameterOrder[category]) {
              commonSpecsParameterOrder[category] = [];
            }

            const categorySpecs = config.specs[category];
            if (typeof categorySpecs === 'object' && categorySpecs !== null) {
              Object.keys(categorySpecs).forEach(param => {
                parametersByCategory[category].set(param, true);
              });
            }
          });
        }
      });
    }
  });

  // 第三步：按照commonSpecs的顺序排列参数，额外的参数放在后面
  const finalParametersByCategory = {};
  Object.keys(parametersByCategory).forEach(category => {
    const allParams = Array.from(parametersByCategory[category].keys());
    const commonParams = commonSpecsParameterOrder[category] || [];
    const extraParams = allParams.filter(param => !commonParams.includes(param)).sort();

    // 先放commonSpecs中的参数（保持原始顺序），再放额外的参数（字母排序）
    finalParametersByCategory[category] = [...commonParams, ...extraParams];
  });

  const sortedCategories = Object.keys(finalParametersByCategory).sort();

  // 统计信息
  const totalParameters = Object.values(finalParametersByCategory).reduce((sum, params) => sum + params.length, 0);

  console.log(`✅ 全面收集完成:`);
  console.log(`   - 参数分类数量: ${allCategories.size}`);
  console.log(`   - 总参数数量: ${totalParameters}`);
  console.log(`   - 分类列表: ${sortedCategories.slice(0, 5).join(', ')}${sortedCategories.length > 5 ? '...' : ''}`);
  console.log(`   - 参数排序策略: 优先按commonSpecs顺序，额外参数字母排序`);

  return {
    totalCategories: allCategories.size,
    totalParameters: totalParameters,
    categories: sortedCategories,
    parametersByCategory: finalParametersByCategory
  };
}

/**
 * 构建完整的产品参数对比数据
 * @param {Array} formattedProducts 格式化后的产品数据
 * @param {Object} parameterInfo 参数信息
 * @returns {Object} 完整对比数据
 */
function buildComparisonTable(formattedProducts, parameterInfo) {
  console.log('🔧 构建完整参数对比数据...');

  const comparisonData = {};

  // 为每个分类构建对比数据
  parameterInfo.categories.forEach(category => {
    comparisonData[category] = {};

    // 为每个参数收集所有产品的值
    parameterInfo.parametersByCategory[category].forEach(parameter => {
      comparisonData[category][parameter] = {};

      formattedProducts.forEach(product => {
        const allValues = new Set(); // 使用Set避免重复值

        // 从 commonSpecs 获取值
        if (product.commonSpecs?.[category]?.[parameter] !== undefined) {
          const commonValue = product.commonSpecs[category][parameter];
          if (commonValue !== null && commonValue !== undefined && commonValue !== '') {
            allValues.add(String(commonValue).trim());
          }
        }

        // 从所有 configurations 获取值
        if (product.configurations && Array.isArray(product.configurations)) {
          product.configurations.forEach(config => {
            if (config.specs?.[category]?.[parameter] !== undefined) {
              const configValue = config.specs[category][parameter];
              if (configValue !== null && configValue !== undefined && configValue !== '') {
                allValues.add(String(configValue).trim());
              }
            }
          });
        }

        // 处理最终值
        let finalValue;
        if (allValues.size === 0) {
          finalValue = "未明确信息";
        } else if (allValues.size === 1) {
          finalValue = Array.from(allValues)[0];
        } else {
          // 多个值用 / 分隔
          finalValue = Array.from(allValues).sort().join(' / ');
        }

        comparisonData[category][parameter][product.name] = finalValue;
      });
    });
  });

  console.log('✅ 完整对比数据构建完成');
  return comparisonData;
}

/**
 * 根据产品名称列表获取基础产品对比数据（非AI版本）
 * @param {Array<String>} productNames 产品名称列表
 * @returns {Promise<Object>} 对比结果
 */
const compareProductsByNamesBasic = async (productNames) => {
  try {
    console.log('🔍 开始基础产品对比（非AI版本）');
    console.log('待对比产品:', productNames);

    // 1. 验证输入参数
    if (!Array.isArray(productNames) || productNames.length < 2) {
      return {
        success: false,
        error: '至少需要提供2个产品名称进行对比',
        data: null
      };
    }

    if (productNames.length > 6) {
      return {
        success: false,
        error: '最多支持6个产品同时对比',
        data: null
      };
    }

    // 2. 复用现有的产品查找逻辑
    const findResult = await findProductsByNamesV4(productNames);

    if (!findResult.success) {
      return findResult;
    }

    const { products, notFoundProducts } = findResult.data;

    console.log(`✅ 找到 ${products.length} 个产品用于对比`);
    if (notFoundProducts.length > 0) {
      console.log(`⚠️ 未找到的产品: ${notFoundProducts.join(', ')}`);
    }

    // 3. 格式化产品数据
    const formattedProductData = products.map(product => formatProductForAI(product));

    // 4. 整理产品参数信息
    const parameterInfo = organizeProductParameters(formattedProductData);

    // 5. 构建参数对比表格
    const comparisonTable = buildComparisonTable(formattedProductData, parameterInfo);

    // 6. 构建最终返回结果 - 保持最原始的数据结构
    const result = {
      success: true,
      data: {
        // 产品基本信息（最简化）
        products: products.map(product => ({
          skuName: product.skuName,
          imageUrl: product.imageUrl,
          productType: product.productType
        })),

        // 原始产品数据（完全未处理的格式化数据）
        // rawProductData: formattedProductData,

        // 基础参数统计（最简化）
        // basicStats: {
        //   productCategory: productCategory,
        //   isSameCategory: isSameCategory,
        //   totalProducts: products.length,
        //   foundProducts: products.length,
        //   notFoundProducts: notFoundProducts.length
        // },

        // 原始参数对比数据
        rawComparisonData: comparisonTable,

        // 未找到的产品列表
        notFoundProducts: notFoundProducts
      }
    };

    console.log('✅ 基础产品对比完成');
    return result;

  } catch (error) {
    console.error('❌ 基础产品对比失败:', error);
    return {
      success: false,
      error: `基础产品对比失败: ${error.message}`,
      data: null
    };
  }
};

module.exports = {
  compareProductsByNamesBasic,
  organizeProductParameters,
  buildComparisonTable
};
