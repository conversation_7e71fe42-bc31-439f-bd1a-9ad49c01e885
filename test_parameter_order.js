/**
 * 测试参数顺序调整功能
 */

const { organizeProductParameters } = require('./server/src/services/product/productCompareBasic');

// 模拟产品数据
const testProducts = [
  {
    name: "测试产品1",
    commonSpecs: {
      "基本信息": {
        "产品型号": "Test1",
        "发布时间": "2024-01-01",
        "品牌": "TestBrand"
      },
      "外观设计": {
        "长度": "160mm",
        "宽度": "75mm",
        "厚度": "8mm"
      }
    },
    configurations: [
      {
        specs: {
          "基本信息": {
            "特殊版本": "标准版"
          },
          "外观设计": {
            "颜色": "黑色",
            "材质": "金属"
          },
          "存储扩展": {
            "ROM容量": "256GB"
          }
        }
      }
    ]
  }
];

console.log('🧪 开始测试参数顺序...');

const result = organizeProductParameters(testProducts);

console.log('\n📋 测试结果:');
console.log('基本信息参数顺序:', result.parametersByCategory['基本信息']);
console.log('外观设计参数顺序:', result.parametersByCategory['外观设计']);
console.log('存储扩展参数顺序:', result.parametersByCategory['存储扩展']);

console.log('\n✅ 预期结果:');
console.log('基本信息应该是: ["产品型号", "发布时间", "品牌", "特殊版本"]');
console.log('外观设计应该是: ["长度", "宽度", "厚度", "材质", "颜色"]');
console.log('存储扩展应该是: ["ROM容量"]');
